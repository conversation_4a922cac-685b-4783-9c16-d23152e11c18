# 为extension目录所有console.log添加【EchoSync】前缀

## 任务概述
为extension目录下所有的console.log、console.error、console.warn、console.group、console.groupEnd等语句添加【EchoSync】前缀，方便日志收集和识别。

## 需要修改的文件列表

### 1. Background Script
- [ ] `extension/src/background/index.ts` - 21个console语句

### 2. Content Scripts
- [ ] `extension/src/content/index.ts` - 18个console语句
- [ ] `extension/src/content/adapters/claude.ts` - 1个console.error
- [ ] `extension/src/content/adapters/chatgpt.ts` - 1个console.error
- [ ] `extension/src/content/adapters/deepseek.ts` - 1个console.error
- [ ] `extension/src/content/adapters/gemini.ts` - 1个console.error
- [ ] `extension/src/content/adapters/kimi.ts` - 4个console语句

### 3. Base Classes
- [ ] `extension/src/content/base/AIAdapter.ts` - 3个console语句
- [ ] `extension/src/content/base/ArchiveButton.ts` - 3个console语句
- [ ] `extension/src/content/base/FloatingBubble.ts` - 1个console.log
- [ ] `extension/src/content/base/InputManager.ts` - 10个console语句
- [ ] `extension/src/content/base/DragHandler.ts` - 5个console语句
- [ ] `extension/src/content/base/HistoryManager.ts` - 6个console语句

### 4. Library Files
- [ ] `extension/src/lib/messaging.ts` - 5个console.error
- [ ] `extension/src/lib/storage.ts` - 1个console.error
- [ ] `extension/src/lib/debug.ts` - 约50个console语句（各种类型）

### 5. Test Files
- [ ] `extension/src/test/e2e-test.ts` - 48个console语句

### 6. Root Test File
- [ ] `test-fix.js` - 约20个console语句

## 修改规则
1. 将所有 `console.log(...)` 改为 `console.log('【EchoSync】', ...)`
2. 将所有 `console.error(...)` 改为 `console.error('【EchoSync】', ...)`
3. 将所有 `console.warn(...)` 改为 `console.warn('【EchoSync】', ...)`
4. 将所有 `console.group(...)` 改为 `console.group('【EchoSync】', ...)`
5. `console.groupEnd()` 保持不变
6. 对于已经有字符串开头的日志，在字符串前添加【EchoSync】前缀

## 执行顺序
1. 先处理核心文件（background, content主文件）
2. 再处理适配器文件
3. 然后处理base类文件
4. 最后处理工具库和测试文件

## 注意事项
- 保持原有的日志格式和内容不变
- 确保不破坏现有的字符串模板和变量插值
- 对于多行日志，只在第一个参数添加前缀
- 测试修改后的代码确保功能正常
