import { MessagingService } from '../lib/messaging'
import { StorageService } from '../lib/storage'
import { chatHistoryService } from '../lib/storage/chatHistoryDexie'
import { platformService } from '../lib/storage/platformDexie'
import { MessageType, ChromeMessage, Prompt } from '../types'

console.log('EchoSync Background Script loaded')

// 插件安装时的初始化
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('EchoSync installed:', details.reason)
  
  if (details.reason === 'install') {
    // 首次安装，设置默认配置
    const settings = await StorageService.getSettings()
    await StorageService.saveSettings(settings)
    
    // 打开欢迎页面
    chrome.tabs.create({
      url: chrome.runtime.getURL('options/index.html?welcome=true')
    })
  }
})

// 监听来自popup和content script的消息
MessagingService.onMessage(async (message, sender, sendResponse) => {
  console.log('Background received message:', message.type, message.payload)

  try {
    switch (message.type) {
      case MessageType.SYNC_PROMPT:
        await handleSyncPrompt(message.payload, sender)
        sendResponse({ success: true })
        break

      case MessageType.GET_HISTORY:
        try {
          const result = await chatHistoryService.getUniqueChats({ limit: 100 })
          if (result.success) {
            sendResponse({ success: true, data: result.data })
          } else {
            sendResponse({ success: false, error: result.error })
          }
        } catch (error) {
          sendResponse({ success: false, error: 'Failed to get history' })
        }
        break

      case MessageType.SAVE_CONVERSATION:
        await StorageService.addConversation(message.payload)
        sendResponse({ success: true })
        break

      case MessageType.UPDATE_SETTINGS:
        await StorageService.saveSettings(message.payload)
        sendResponse({ success: true })
        break

      case MessageType.CAPTURE_PROMPT:
        await handleCapturePrompt(message.payload, sender)
        sendResponse({ success: true })
        break

      default:
        sendResponse({ success: false, error: 'Unknown message type' })
    }
  } catch (error) {
    console.error('Background message handler error:', error)
    sendResponse({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    })
  }
})

// 处理提示词同步
async function handleSyncPrompt(promptData: any, sender: any) {
  try {
    console.log('Handling sync prompt:', promptData)

    // 根据平台名称获取平台信息
    let platform = null
    if (promptData.platform) {
      // 将平台名称标准化（首字母大写）
      const platformName = promptData.platform.charAt(0).toUpperCase() + promptData.platform.slice(1).toLowerCase()
      const platformResult = await platformService.getByName(platformName)

      if (platformResult.success) {
        platform = platformResult.data
      } else {
        console.warn(`Platform ${platformName} not found, trying to create it`)
        // 如果平台不存在，尝试创建（这里需要根据实际情况设置URL）
        const platformUrls: Record<string, string> = {
          'Deepseek': 'https://chat.deepseek.com',
          'Chatgpt': 'https://chat.openai.com',
          'Claude': 'https://claude.ai',
          'Gemini': 'https://gemini.google.com',
          'Kimi': 'https://kimi.moonshot.cn'
        }

        if (platformUrls[platformName]) {
          const createResult = await platformService.create({
            name: platformName,
            url: platformUrls[platformName]
          })

          if (createResult.success) {
            platform = createResult.data
          }
        }
      }
    }

    if (!platform) {
      console.error('Failed to get or create platform for:', promptData.platform)
      return
    }

    // 使用chatHistoryService保存提示词
    const result = await chatHistoryService.create({
      chat_prompt: promptData.content,
      platform_id: platform.id!,
      chat_uid: Date.now().toString(),
      create_time: promptData.timestamp || Date.now()
    })

    if (result.success) {
      console.log('Prompt saved to IndexedDB:', result.data)
    } else {
      console.error('Failed to save prompt:', result.error)
      return
    }

    // 获取设置，检查是否启用实时同步
    const settings = await StorageService.getSettings()
    if (!settings.syncEnabled) return

    // 获取所有支持的标签页
    const tabs = await chrome.tabs.query({
      url: [
        'https://chat.openai.com/*',
        'https://chat.deepseek.com/*',
        'https://claude.ai/*',
        'https://gemini.google.com/*',
        'https://kimi.moonshot.cn/*'
      ]
    })

    // 向其他标签页发送同步消息
    const syncPromises = tabs
      .filter(tab => tab.id !== sender.tab?.id) // 排除发送者标签页
      .map(tab => {
        if (tab.id) {
          return MessagingService.sendToContentScript(
            tab.id,
            MessageType.INJECT_PROMPT,
            { prompt: promptData.content }
          ).catch(error => {
            console.log(`Failed to sync to tab ${tab.id}:`, error)
          })
        }
      })

    await Promise.all(syncPromises)
  } catch (error) {
    console.error('Error in handleSyncPrompt:', error)
  }
}

// 处理提示词捕获
async function handleCapturePrompt(data: any, sender: any) {
  try {
    console.log('Handling capture prompt:', data)

    // 根据平台名称获取平台信息
    let platform = null
    if (data.platform) {
      const platformName = data.platform.charAt(0).toUpperCase() + data.platform.slice(1).toLowerCase()
      const platformResult = await platformService.getByName(platformName)

      if (platformResult.success) {
        platform = platformResult.data
      }
    }

    if (!platform) {
      console.error('Failed to get platform for capture:', data.platform)
      return
    }

    // 使用chatHistoryService保存提示词
    const result = await chatHistoryService.create({
      chat_prompt: data.content,
      platform_id: platform.id!,
      chat_uid: Date.now().toString(),
      create_time: Date.now()
    })

    if (result.success) {
      console.log('Captured prompt saved to IndexedDB:', result.data)

      // 通知popup更新
      try {
        chrome.runtime.sendMessage({
          type: 'PROMPT_CAPTURED',
          payload: {
            id: result.data.id,
            content: data.content,
            platform: data.platform,
            timestamp: Date.now()
          }
        })
      } catch (error) {
        // Popup可能未打开，忽略错误
        console.log('Failed to notify popup:', error)
      }
    } else {
      console.error('Failed to capture prompt:', result.error)
    }
  } catch (error) {
    console.error('Error in handleCapturePrompt:', error)
  }
}

// 监听标签页更新，检测AI平台
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    const supportedDomains = [
      'chat.openai.com',
      'chat.deepseek.com',
      'claude.ai',
      'gemini.google.com'
    ]

    const isSupported = supportedDomains.some(domain => tab.url && tab.url.includes(domain))
    
    if (isSupported) {
      // 注入content script（如果需要）
      try {
        await chrome.scripting.executeScript({
          target: { tabId },
          files: ['content/index.js']
        })
      } catch (error) {
        // 可能已经注入过了，忽略错误
      }
    }
  }
})

// 处理快捷键
chrome.commands.onCommand.addListener(async (command) => {
  console.log('Command received:', command)
  
  switch (command) {
    case 'open-popup':
      // 打开popup（通过点击图标实现）
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (tab.id) {
        chrome.action.openPopup()
      }
      break
      
    case 'quick-sync':
      // 快速同步当前页面的提示词
      const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (currentTab.id && await MessagingService.isTabSupported(currentTab.id)) {
        MessagingService.sendToContentScript(
          currentTab.id,
          MessageType.CAPTURE_PROMPT,
          {}
        )
      }
      break
  }
})

// 保持service worker活跃
chrome.runtime.onStartup.addListener(() => {
  console.log('EchoSync service worker started')
})

// 定期清理过期数据
setInterval(async () => {
  try {
    const oneMonthAgo = Date.now() - (30 * 24 * 60 * 60 * 1000)

    // 获取所有聊天历史
    const result = await chatHistoryService.getList({ limit: 10000 })
    if (result.success) {
      const oldRecords = result.data.data.filter(record => record.create_time < oneMonthAgo)

      // 删除过期记录
      let deletedCount = 0
      for (const record of oldRecords) {
        const deleteResult = await chatHistoryService.delete(record.id)
        if (deleteResult.success) {
          deletedCount++
        }
      }

      if (deletedCount > 0) {
        console.log(`Cleaned up ${deletedCount} old chat history records`)
      }
    }
  } catch (error) {
    console.error('Cleanup error:', error)
  }
}, 24 * 60 * 60 * 1000) // 每24小时执行一次
