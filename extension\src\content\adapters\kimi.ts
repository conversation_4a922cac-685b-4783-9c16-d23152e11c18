import { AIAdapter } from '../base'
import { Conversation, Message, AIPlatform } from '@/types'

export class <PERSON><PERSON><PERSON><PERSON>pter extends AIAdapter {
  constructor() {
    console.log('【EchoSync】KimiAdapter constructor called')

    const platform = {
      name: '<PERSON><PERSON>',
      id: 'kimi' as AIPlatform,
      url: 'https://kimi.moonshot.cn'
    }

    const selectors = {
      inputField: '[data-lexical-editor="true"], .chat-input-editor[contenteditable="true"], .chat-input-editor, [contenteditable="true"]',
      sendButton: '.send-button-container:not(.disabled) .send-button, .send-button:not(.disabled), .chat-editor-action .send-button-container:not(.disabled) .send-button',
      messageContainer: '.chat-content-item, .segment'
    }

    super(platform, selectors)
    console.log('【EchoSync】KimiAdapter initialized with selectors:', selectors)
  }

  async injectPrompt(prompt: string): Promise<void> {
    // 使用输入管理器注入提示词
    this.inputManager.injectPrompt(prompt)
  }

  async extractConversation(): Promise<Conversation | null> {
    try {
      const messageElements = document.querySelectorAll(this.selectors.messageContainer)
      if (messageElements.length === 0) return null

      const messages: Message[] = []

      messageElements.forEach((element, index) => {
        // Kimi使用特定的类名来区分用户和助手消息
        const isUser = element.classList.contains('chat-content-item-user') ||
                      element.classList.contains('segment-user') ||
                      element.querySelector('.user-content') !== null

        const isAssistant = element.classList.contains('chat-content-item-assistant') ||
                           element.classList.contains('segment-assistant') ||
                           element.querySelector('.markdown-container') !== null

        // 只处理用户或助手消息
        if (!isUser && !isAssistant) return

        const contentElement = element.querySelector('.user-content') ||
                              element.querySelector('.markdown-container .markdown') ||
                              element.querySelector('.segment-content-box') ||
                              element

        if (contentElement) {
          const content = contentElement.textContent?.trim() || ''
          if (content) {
            messages.push({
              id: `msg-${index}`,
              role: isUser ? 'user' : 'assistant',
              content,
              timestamp: Date.now() - (messageElements.length - index) * 1000
            })
          }
        }
      })

      if (messages.length === 0) return null

      // 获取对话标题
      const titleElement = document.querySelector('.chat-header-content h2, .chat-name')
      const title = titleElement?.textContent?.trim() || `Kimi对话 - ${new Date().toLocaleDateString()}`

      return {
        id: `kimi-${Date.now()}`,
        platform: 'kimi',
        title,
        messages,
        createdAt: Math.min(...messages.map(m => m.timestamp)),
        updatedAt: Math.max(...messages.map(m => m.timestamp))
      }
    } catch (error) {
      console.error('【EchoSync】Extract Kimi conversation error:', error)
      return null
    }
  }

  isValidPage(): boolean {
    const hostname = window.location.hostname
    const isValid = hostname.includes('kimi.moonshot.cn') || hostname.includes('kimi.com')
    console.log('【EchoSync】KimiAdapter.isValidPage() - hostname:', hostname, 'isValid:', isValid)
    return isValid
  }

  // 重写发送按钮检测，适配Kimi的特殊结构
  protected findSendButton(): HTMLElement | null {
    const selectors = [
      '.send-button-container:not(.disabled) .send-button',
      '.chat-editor-action .send-button-container:not(.disabled) .send-button',
      '.send-button:not(.disabled)',
      '.send-button-container .send-button'
    ]

    for (const selector of selectors) {
      const button = document.querySelector(selector) as HTMLElement
      if (button) {
        // 检查按钮本身和容器是否被禁用
        const container = button.closest('.send-button-container')
        const isDisabled = button.classList.contains('disabled') ||
                          container?.classList.contains('disabled') ||
                          button.hasAttribute('disabled')

        if (!isDisabled) {
          console.log('【EchoSync】Found active send button:', selector)
          return button
        }
      }
    }

    console.log('【EchoSync】No active send button found')
    return null
  }

  /**
   * 重写输入内容获取方法，适配Kimi的Lexical编辑器
   */
  getCurrentInput(): string {
    const inputElement = this.inputManager.getInputElement()
    if (!inputElement) return ''

    // Kimi使用Lexical编辑器，需要特殊处理
    if (inputElement.hasAttribute('data-lexical-editor')) {
      // 获取纯文本内容，排除<br>标签
      const textContent = inputElement.textContent || ''
      return textContent.trim()
    }

    // 回退到默认方法
    if (inputElement.tagName === 'TEXTAREA' || inputElement.tagName === 'INPUT') {
      return (inputElement as HTMLInputElement).value
    } else if (inputElement.contentEditable === 'true') {
      return inputElement.textContent || ''
    }

    return ''
  }

  /**
   * 重写输入监听设置，添加Kimi特定的事件处理
   */
  setupInputMonitoring(): void {
    const inputElement = this.inputManager.getInputElement()
    if (!inputElement) return

    // 监听输入变化
    const handleInput = () => {
      const content = this.getCurrentInput()
      console.log('【EchoSync】Kimi input changed:', content)

      // 触发输入变化事件
      document.dispatchEvent(new CustomEvent('echosync:input-changed', {
        detail: { content, platform: 'kimi' }
      }))
    }

    // 为Lexical编辑器添加特殊的事件监听
    if (inputElement.hasAttribute('data-lexical-editor')) {
      // Lexical编辑器使用MutationObserver来监听内容变化
      const observer = new MutationObserver(handleInput)
      observer.observe(inputElement, {
        childList: true,
        subtree: true,
        characterData: true
      })
    }

    // 标准事件监听
    inputElement.addEventListener('input', handleInput)
    inputElement.addEventListener('keyup', handleInput)
    inputElement.addEventListener('paste', () => {
      setTimeout(handleInput, 100) // 延迟处理粘贴事件
    })

    console.log('【EchoSync】Kimi input monitoring set up')
  }


}
