import { AIPlatform, Conversation } from '@/types'
import { Platform } from '@/types/database'
import { platformService } from '@/lib/storage/platformDexie'
import { DOMUtils } from './DOMUtils'
import { FloatingBubble } from './FloatingBubble'
import { ArchiveButton } from './ArchiveButton'
import { Drag<PERSON>and<PERSON> } from './DragHandler'
import { InputManager } from './InputManager'
import { HistoryManager } from './HistoryManager'

/**
 * AI适配器基类
 * 重构后的主适配器类，集成所有拆分的模块
 */
export abstract class AIAdapter {
  // 基础属性
  protected platform: { name: string; id: AIPlatform; url: string }
  protected selectors: { inputField: string; sendButton: string; messageContainer: string }
  protected currentPlatform: Platform | null = null

  // 模块实例
  protected floatingBubble: FloatingBubble
  protected archiveButton: ArchiveButton
  protected dragHandler: DragHandler | null = null
  protected inputManager: InputManager
  protected historyManager: HistoryManager

  constructor(platform: { name: string; id: AIPlatform; url: string }, selectors: any) {
    this.platform = platform
    this.selectors = selectors

    // 初始化模块
    this.floatingBubble = new FloatingBubble()
    this.archiveButton = new ArchiveButton()
    this.inputManager = new InputManager()
    this.historyManager = new HistoryManager()

    // 设置事件监听
    this.setupEventListeners()
  }

  /**
   * 抽象方法 - 子类必须实现
   */
  abstract injectPrompt(prompt: string): Promise<void>
  abstract extractConversation(): Promise<Conversation | null>
  abstract isValidPage(): boolean

  /**
   * 初始化通用功能
   */
  async initUniversalFeatures(): Promise<void> {
    console.log(`Initializing universal features for ${this.platform.name}...`)

    try {
      // 等待页面加载
      console.log('Waiting for page load...')
      await DOMUtils.waitForPageLoad()
      console.log('Page loaded')

      // 加载当前平台信息
      console.log('Loading current platform...')
      await this.loadCurrentPlatform()
      console.log('Current platform loaded:', this.currentPlatform)

      // 查找并设置输入元素
      console.log('Finding and setting up input element...')
      await this.inputManager.findAndSetupInputElement(this.selectors)
      console.log('Input element setup complete')

      // 创建悬浮气泡
      console.log('Creating floating bubble...')
      const bubble = this.floatingBubble.createFloatingBubble()
      console.log('Floating bubble created:', bubble)

      if (bubble) {
        // 初始化拖拽处理
        console.log('Initializing drag handler...')
        this.dragHandler = new DragHandler(bubble)
        console.log('Drag handler initialized')

        // 将拖拽处理器暴露给气泡元素，以便点击事件检查
        ;(bubble as any).dragHandler = this.dragHandler
      }

      // 添加存档按钮
      console.log('Adding archive button...')
      await this.archiveButton.addArchiveButton(this.selectors)
      console.log('Archive button added')

      // 设置输入监听
      console.log('Setting up input listeners...')
      this.inputManager.setupInputFocusListener(this.selectors)
      this.inputManager.setupSendListener(this.selectors)
      console.log('Input listeners setup complete')

      console.log('Universal features initialized successfully')
    } catch (error) {
      console.error('Error initializing universal features:', error)
      console.error('Error stack:', error.stack)
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 输入聚焦事件
    document.addEventListener('echosync:input-focused', (event: any) => {
      const { inputElement } = event.detail
      this.floatingBubble.moveToInputField(inputElement)
    })

    // 输入变化事件
    document.addEventListener('echosync:input-changed', (event: any) => {
      const { value } = event.detail
      this.archiveButton.updateArchiveButtonState(value)
    })

    // 自动存档事件
    document.addEventListener('echosync:auto-archive', (event: any) => {
      const { prompt } = event.detail
      this.archiveButton.autoArchivePrompt(prompt, this.currentPlatform)
    })

    // 存档当前提示词事件
    document.addEventListener('echosync:archive-current-prompt', () => {
      this.archiveButton.archiveCurrentPrompt(
        () => this.inputManager.getCurrentInput(),
        this.currentPlatform,
        (message, type) => this.showNotification(message, type)
      )
    })

    // 注入提示词事件
    document.addEventListener('echosync:inject-prompt', (event: any) => {
      const { prompt } = event.detail
      this.inputManager.injectPrompt(prompt)
    })

    // 边界回弹事件
    document.addEventListener('echosync:snap-to-boundary', () => {
      this.floatingBubble.snapToBoundary()
    })

    // 处理历史点击事件
    document.addEventListener('echosync:handle-history-click', (event: any) => {
      const { chat } = event.detail
      this.handleHistoryItemClick(chat)
    })

    // 请求历史数据事件
    document.addEventListener('echosync:request-history-data', () => {
      this.historyManager.showHistoryBubble()
    })

    // 显示存储的提示词事件
    document.addEventListener('echosync:show-stored-prompts', () => {
      this.historyManager.showHistoryBubble()
    })

    // 调试功能事件
    document.addEventListener('echosync:debug-features', () => {
      this.debugFeatures()
    })
  }

  /**
   * 加载当前平台信息
   */
  protected async loadCurrentPlatform(): Promise<void> {
    try {
      const platformsResult = await platformService.getAll()
      if (platformsResult.success) {
        this.currentPlatform = platformsResult.data.find(p => 
          p.name === this.platform.name || 
          window.location.href.includes(p.url.replace('https://', '').replace('http://', ''))
        ) || null

        if (this.currentPlatform) {
          console.log('Current platform loaded:', this.currentPlatform)
        } else {
          console.warn('Current platform not found in database')
        }
      }
    } catch (error) {
      console.error('Error loading current platform:', error)
    }
  }

  /**
   * 显示通知
   */
  protected showNotification(message: string, type: string = 'info'): void {
    // 创建简单的通知显示
    const notification = document.createElement('div')
    notification.textContent = message
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
      color: white;
      border-radius: 8px;
      z-index: 10002;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
    `

    document.body.appendChild(notification)

    // 3秒后自动移除
    setTimeout(() => {
      notification.style.opacity = '0'
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 300)
    }, 3000)
  }

  /**
   * 处理历史项点击
   */
  protected handleHistoryItemClick(chat: any): void {
    console.log('Handling history item click:', chat)
    this.injectPrompt(chat.chat_prompt)
  }

  /**
   * 获取当前输入内容
   */
  protected getCurrentInput(): string {
    return this.inputManager.getCurrentInput()
  }

  /**
   * 获取平台名称
   */
  getPlatformName(): string {
    console.log('getPlatformName called, platform:', this.platform)
    return this.platform?.name || 'Unknown'
  }

  /**
   * 获取选择器
   */
  getSelectors(): { inputField: string; sendButton: string; messageContainer: string } {
    return this.selectors
  }

  /**
   * 调试功能
   */
  protected debugFeatures(): void {
    console.group('【EchoSync】=== AI Adapter Debug Info ===')
    console.log('【EchoSync】Platform:', this.platform)
    console.log('【EchoSync】Current Platform:', this.currentPlatform)
    console.log('【EchoSync】Selectors:', this.selectors)
    console.log('【EchoSync】Input Element:', this.inputManager.getInputElement())
    console.log('【EchoSync】Floating Bubble:', this.floatingBubble.getBubble())
    console.log('【EchoSync】Archive Button:', this.archiveButton.getButton())
    console.groupEnd()
  }

  /**
   * 销毁适配器
   */
  destroy(): void {
    this.floatingBubble.destroy()
    this.archiveButton.destroy()
    this.dragHandler?.destroy()
    this.inputManager.destroy()
    this.historyManager.destroy()

    console.log('【EchoSync】AI Adapter destroyed')
  }
}
