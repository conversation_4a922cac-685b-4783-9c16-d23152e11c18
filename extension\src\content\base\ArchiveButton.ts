import { chatHistoryService } from '@/lib/storage/chatHistoryDexie'
import { Platform } from '@/types/database'
import { DOMUtils } from './DOMUtils'

/**
 * 存档按钮管理类
 * 从base.ts中提取的存档按钮相关方法
 */
export class ArchiveButton {
  private button: HTMLElement | null = null
  private currentPromptId: string = ''
  private archivedPromptIds: Set<string> = new Set()

  constructor() {
    this.generateNewPromptId()
  }

  /**
   * 添加存档按钮
   */
  async addArchiveButton(selectors: { inputField: string }): Promise<void> {
    // 等待输入框出现
    const inputElement = await DOMUtils.waitForElement(selectors.inputField) as HTMLElement
    if (!inputElement) return

    // 找到输入框的父容器
    const inputContainer = DOMUtils.findInputContainer(inputElement)
    if (!inputContainer) return

    // 创建存档按钮
    this.button = document.createElement('div')
    this.button.className = 'echosync-archive-button'
    this.button.innerHTML = '📁'
    this.button.title = '存档当前提示词'

    // 设置按钮样式
    this.button.style.cssText = `
      position: fixed;
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10001;
      box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      color: white;
      font-size: 20px;
      opacity: 0;
      transform: scale(0.8);
      pointer-events: none;
    `

    // 定位按钮
    this.positionArchiveButton(inputContainer)

    // 添加点击事件
    this.button.addEventListener('click', () => {
      document.dispatchEvent(new CustomEvent('echosync:archive-current-prompt'))
    })

    document.body.appendChild(this.button)
    console.log('【EchoSync】Archive button created and positioned')
  }

  /**
   * 定位存档按钮到输入框右侧
   */
  private positionArchiveButton(inputContainer: HTMLElement): void {
    if (!this.button) return

    const updatePosition = () => {
      const rect = inputContainer.getBoundingClientRect()
      const buttonSize = 50
      const margin = 15

      this.button!.style.left = `${rect.right + margin}px`
      this.button!.style.top = `${rect.top + (rect.height - buttonSize) / 2}px`
    }

    updatePosition()

    // 监听窗口大小变化和滚动
    window.addEventListener('resize', updatePosition)
    window.addEventListener('scroll', updatePosition)

    // 监听输入框位置变化
    const resizeObserver = new ResizeObserver(updatePosition)
    resizeObserver.observe(inputContainer)
  }

  /**
   * 更新存档按钮状态
   */
  updateArchiveButtonState(inputValue: string): void {
    if (!this.button) return

    const hasContent = inputValue.trim().length > 0
    const isArchived = this.archivedPromptIds.has(this.currentPromptId)

    if (hasContent && !isArchived) {
      // 显示按钮并添加发光效果
      this.showArchiveButtonWithGlow()
    } else if (isArchived) {
      // 显示已存档状态
      this.showArchivedState()
    } else {
      // 隐藏按钮
      this.hideArchiveButton()
    }
  }

  /**
   * 显示存档按钮并添加发光效果
   */
  private showArchiveButtonWithGlow(): void {
    if (!this.button) return

    this.button.style.opacity = '1'
    this.button.style.transform = 'scale(1)'
    this.button.style.pointerEvents = 'auto'

    // 添加贝塞尔曲线发光动画
    this.button.style.animation = 'echosync-glow 2s ease-in-out infinite'
    this.button.classList.remove('archived')

    // 添加发光动画样式
    if (!document.getElementById('echosync-glow-styles')) {
      const style = document.createElement('style')
      style.id = 'echosync-glow-styles'
      style.textContent = `
        @keyframes echosync-glow {
          0%, 100% {
            box-shadow: 0 4px 20px rgba(139, 92, 246, 0.3);
          }
          50% {
            box-shadow: 0 4px 30px rgba(139, 92, 246, 0.6), 0 0 20px rgba(139, 92, 246, 0.4);
          }
        }
      `
      document.head.appendChild(style)
    }
  }

  /**
   * 显示已存档状态
   */
  private showArchivedState(): void {
    if (!this.button) return

    this.button.style.opacity = '1'
    this.button.style.transform = 'scale(1)'
    this.button.style.pointerEvents = 'none'
    this.button.style.animation = 'none'
    this.button.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
    this.button.innerHTML = '✓'
    this.button.classList.add('archived')
  }

  /**
   * 隐藏存档按钮
   */
  private hideArchiveButton(): void {
    if (!this.button) return

    this.button.style.opacity = '0'
    this.button.style.transform = 'scale(0.8)'
    this.button.style.pointerEvents = 'none'
    this.button.style.animation = 'none'
  }

  /**
   * 自动存档提示词（发送时调用）
   */
  async autoArchivePrompt(promptContent: string, currentPlatform: Platform | null): Promise<void> {
    try {
      if (!currentPlatform) {
        console.warn('无法识别当前平台，跳过自动存档')
        return
      }

      // 生成新的提示词ID
      this.generateNewPromptId()

      // 直接使用 Dexie 存储
      const result = await chatHistoryService.create({
        chat_prompt: promptContent,
        chat_uid: this.currentPromptId,
        platform_id: currentPlatform.id!,
        create_time: Date.now()
      })

      if (result.success) {
        // 标记为已存档
        this.archivedPromptIds.add(this.currentPromptId)
        console.log('Prompt auto-archived:', result.data)
      } else {
        console.error('Auto archive failed:', result.error)
      }
    } catch (error) {
      console.error('Auto archive prompt error:', error)
    }
  }

  /**
   * 存档当前提示词
   */
  async archiveCurrentPrompt(
    getCurrentInput: () => string, 
    currentPlatform: Platform | null,
    showNotification: (message: string, type: string) => void
  ): Promise<void> {
    const currentPrompt = getCurrentInput()
    if (!currentPrompt || currentPrompt.trim().length === 0) {
      showNotification('输入框为空，无法存档', 'error')
      return
    }

    try {
      if (!currentPlatform) {
        showNotification('无法识别当前平台', 'error')
        return
      }

      // 直接使用 Dexie 存储
      const result = await chatHistoryService.create({
        chat_prompt: currentPrompt,
        chat_uid: this.currentPromptId,
        platform_id: currentPlatform.id!,
        create_time: Date.now()
      })

      if (result.success) {
        // 标记为已存档
        this.archivedPromptIds.add(this.currentPromptId)

        // 更新按钮状态
        this.showArchivedState()

        // 显示成功通知和动画
        showNotification('提示词已存档', 'success')
        this.showArchiveAnimation()

        console.log('【EchoSync】Prompt archived:', result.data)
      } else {
        showNotification(result.error || '存档失败', 'error')
      }
    } catch (error) {
      console.error('【EchoSync】Archive prompt error:', error)
      showNotification('存档失败', 'error')
    }
  }

  /**
   * 显示存档动画
   */
  private showArchiveAnimation(): void {
    if (!this.button) return

    // 创建飞行动画元素
    const flyingIcon = this.button.cloneNode(true) as HTMLElement
    flyingIcon.style.cssText = `
      position: fixed;
      z-index: 10001;
      pointer-events: none;
      transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    `

    const buttonRect = this.button.getBoundingClientRect()
    flyingIcon.style.left = `${buttonRect.left}px`
    flyingIcon.style.top = `${buttonRect.top}px`

    document.body.appendChild(flyingIcon)

    // 动画到右上角
    setTimeout(() => {
      flyingIcon.style.left = `${window.innerWidth - 100}px`
      flyingIcon.style.top = '20px'
      flyingIcon.style.transform = 'scale(0.3)'
      flyingIcon.style.opacity = '0'
    }, 50)

    // 清理动画元素
    setTimeout(() => {
      document.body.removeChild(flyingIcon)
    }, 650)
  }

  /**
   * 生成新的提示词ID
   */
  generateNewPromptId(): void {
    this.currentPromptId = `prompt-${Date.now()}`
  }

  /**
   * 获取当前提示词ID
   */
  getCurrentPromptId(): string {
    return this.currentPromptId
  }

  /**
   * 获取已存档的提示词ID集合
   */
  getArchivedPromptIds(): Set<string> {
    return this.archivedPromptIds
  }

  /**
   * 获取按钮元素
   */
  getButton(): HTMLElement | null {
    return this.button
  }

  /**
   * 销毁
   */
  destroy(): void {
    if (this.button) {
      this.button.remove()
      this.button = null
    }
  }
}
