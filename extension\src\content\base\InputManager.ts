import { DOMUtils } from './DOMUtils'

/**
 * 输入管理类
 * 从base.ts中提取的输入框管理相关方法
 */
export class InputManager {
  private inputElement: HTMLElement | null = null
  private sendButtonContainer: HTMLElement | null = null
  private lastInputValue: string = ''

  constructor() {}

  /**
   * 设置输入聚焦监听
   */
  setupInputFocusListener(selectors: { inputField: string }): void {
    // 监听全局点击事件
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement
      if (target && DOMUtils.isInputElement(target)) {
        // 检查是否匹配选择器
        if (target.matches(selectors.inputField)) {
          this.inputElement = target
          console.log('【EchoSync】Input element focused:', target)
          
          // 触发输入框聚焦事件
          document.dispatchEvent(new CustomEvent('echosync:input-focused', { 
            detail: { inputElement: target } 
          }))
        }
      }
    })

    // 监听焦点事件
    document.addEventListener('focusin', (e) => {
      const target = e.target as HTMLElement
      if (target && target.matches(selectors.inputField)) {
        this.inputElement = target
        console.log('【EchoSync】Input element focused via focusin:', target)
        
        // 触发输入框聚焦事件
        document.dispatchEvent(new CustomEvent('echosync:input-focused', { 
          detail: { inputElement: target } 
        }))
      }
    })

    console.log('【EchoSync】Input focus listener set up')
  }

  /**
   * 查找并设置输入元素
   */
  async findAndSetupInputElement(selectors: { inputField: string }): Promise<HTMLElement | null> {
    // 首先尝试直接查找
    let inputElement = document.querySelector(selectors.inputField) as HTMLElement
    
    if (!inputElement) {
      // 如果没找到，尝试通用选择器
      const universalSelectors = DOMUtils.getUniversalInputSelectors()
      for (const selector of universalSelectors) {
        inputElement = document.querySelector(selector) as HTMLElement
        if (inputElement && DOMUtils.isVisibleElement(inputElement)) {
          break
        }
      }
    }

    if (!inputElement) {
      // 等待元素出现
      inputElement = await DOMUtils.waitForElement(selectors.inputField) as HTMLElement
    }

    if (inputElement) {
      this.inputElement = inputElement
      console.log('【EchoSync】Input element found and set:', inputElement)
      
      // 设置输入监听
      this.setupInputMonitoring()
    }

    return inputElement
  }

  /**
   * 设置输入监听
   */
  private setupInputMonitoring(): void {
    if (!this.inputElement) return

    // 监听输入变化
    const handleInput = () => {
      const currentValue = this.getCurrentInput()
      if (currentValue !== this.lastInputValue) {
        this.lastInputValue = currentValue
        
        // 触发输入变化事件
        document.dispatchEvent(new CustomEvent('echosync:input-changed', {
          detail: { value: currentValue }
        }))
      }
    }

    this.inputElement.addEventListener('input', handleInput)
    this.inputElement.addEventListener('change', handleInput)
    this.inputElement.addEventListener('keyup', handleInput)

    console.log('【EchoSync】Input monitoring set up')
  }

  /**
   * 设置发送监听
   */
  setupSendListener(selectors: { sendButton: string }): void {
    // 监听键盘事件（Enter键）
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey && this.inputElement && 
          document.activeElement === this.inputElement) {
        
        // 检查是否可以发送
        if (DOMUtils.canSendMessage(selectors)) {
          console.log('【EchoSync】Send triggered by Enter key')
          this.handleSendEvent()
        }
      }
    })

    // 监听发送按钮点击
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement
      console.log('【EchoSync】Click event detected on element:', target.tagName, target.className)

      if (this.isSendButton(target, selectors.sendButton)) {
        console.log('【EchoSync】Send triggered by button click on:', target)
        console.log('【EchoSync】Current input content before send:', this.getCurrentInput())
        this.handleSendEvent()
      } else {
        // 检查是否点击了发送按钮相关的元素
        const sendButton = target.closest('.send-button')
        const sendContainer = target.closest('.send-button-container')

        if (sendButton || sendContainer) {
          console.log('【EchoSync】Clicked on send-related element but not recognized as send button')
          console.log('【EchoSync】Send button element:', sendButton)
          console.log('【EchoSync】Send container element:', sendContainer)
          console.log('【EchoSync】Container disabled:', sendContainer?.classList.contains('disabled'))
        }
      }
    })

    console.log('【EchoSync】Send listener set up')
  }

  /**
   * 检查是否是发送按钮
   */
  private isSendButton(element: HTMLElement, sendButtonSelector: string): boolean {
    if (!element) {
      console.log('【EchoSync】isSendButton: No element provided')
      return false
    }

    console.log('【EchoSync】isSendButton: Checking element:', element.tagName, element.className)
    console.log('【EchoSync】isSendButton: Using selector:', sendButtonSelector)

    // 检查元素本身
    if (element.matches(sendButtonSelector)) {
      console.log('【EchoSync】isSendButton: Element matches selector directly')

      // 对于Kimi，还需要检查容器是否被禁用
      const container = element.closest('.send-button-container')
      if (container && container.classList.contains('disabled')) {
        console.log('【EchoSync】isSendButton: Send button container is disabled')
        return false
      }
      console.log('【EchoSync】isSendButton: Element is valid send button')
      return true
    }

    // 检查父元素（最多向上查找5层，因为Kimi的结构较深）
    console.log('【EchoSync】isSendButton: Checking parent elements...')
    let parent = element.parentElement
    let depth = 0
    while (parent && depth < 5) {
      console.log(`【EchoSync】isSendButton: Checking parent at depth ${depth}:`, parent.tagName, parent.className)

      if (parent.matches(sendButtonSelector)) {
        console.log('【EchoSync】isSendButton: Parent matches selector at depth', depth)

        // 同样检查容器状态
        const container = parent.closest('.send-button-container')
        if (container && container.classList.contains('disabled')) {
          console.log('【EchoSync】isSendButton: Send button parent container is disabled')
          return false
        }
        console.log('【EchoSync】isSendButton: Parent is valid send button')
        return true
      }
      parent = parent.parentElement
      depth++
    }

    // 检查是否点击的是发送按钮的子元素（如SVG图标）
    console.log('【EchoSync】isSendButton: Checking if element is child of send button...')
    const closestSendButton = element.closest('.send-button')
    if (closestSendButton) {
      console.log('【EchoSync】isSendButton: Element is child of send button:', closestSendButton)

      const sendButton = closestSendButton as HTMLElement
      const container = sendButton.closest('.send-button-container')
      if (container && container.classList.contains('disabled')) {
        console.log('【EchoSync】isSendButton: Send button (from child) container is disabled')
        return false
      }
      console.log('【EchoSync】isSendButton: Child of valid send button')
      return true
    }

    // 检查常见的发送按钮特征
    console.log('【EchoSync】isSendButton: Checking text/aria-label characteristics...')
    const text = element.textContent?.toLowerCase() || ''
    const ariaLabel = element.getAttribute('aria-label')?.toLowerCase() || ''

    const hasTextFeatures = text.includes('send') || text.includes('发送') ||
                           ariaLabel.includes('send') || ariaLabel.includes('发送')

    console.log('【EchoSync】isSendButton: Text features check result:', hasTextFeatures)
    console.log('【EchoSync】isSendButton: Element text:', text)
    console.log('【EchoSync】isSendButton: Element aria-label:', ariaLabel)

    return hasTextFeatures
  }

  /**
   * 处理发送事件
   */
  private async handleSendEvent(): Promise<void> {
    console.log('【EchoSync】InputManager handleSendEvent triggered')

    // 获取发送前的输入内容
    const promptContent = this.getCurrentInput()
    console.log('【EchoSync】Captured prompt content:', promptContent)

    if (promptContent && promptContent.trim().length > 0) {
      const trimmedPrompt = promptContent.trim()
      console.log('【EchoSync】Dispatching auto-archive event with prompt:', trimmedPrompt)

      // 触发自动存档事件
      document.dispatchEvent(new CustomEvent('echosync:auto-archive', {
        detail: { prompt: trimmedPrompt }
      }))

      console.log('【EchoSync】Auto-archive event dispatched successfully')
    } else {
      console.log('【EchoSync】No prompt content to archive (empty or whitespace only)')
    }

    // 延迟检查是否有新消息出现
    setTimeout(() => {
      console.log('【EchoSync】Dispatching check-new-message event')
      document.dispatchEvent(new CustomEvent('echosync:check-new-message'))
    }, 1000)
  }

  /**
   * 获取当前输入内容
   */
  getCurrentInput(): string {
    if (!this.inputElement) return ''

    if (this.inputElement.tagName === 'TEXTAREA' || this.inputElement.tagName === 'INPUT') {
      return (this.inputElement as HTMLInputElement).value
    } else if (this.inputElement.contentEditable === 'true') {
      return this.inputElement.textContent || ''
    }

    return ''
  }

  /**
   * 注入提示词到输入框
   */
  injectPrompt(prompt: string): void {
    if (!this.inputElement) {
      console.warn('【EchoSync】No input element found for prompt injection')
      return
    }

    DOMUtils.simulateUserInput(this.inputElement, prompt)
    console.log('【EchoSync】Prompt injected:', prompt)
  }

  /**
   * 获取输入元素
   */
  getInputElement(): HTMLElement | null {
    return this.inputElement
  }

  /**
   * 获取发送按钮容器
   */
  getSendButtonContainer(): HTMLElement | null {
    return this.sendButtonContainer
  }

  /**
   * 设置发送按钮容器
   */
  setSendButtonContainer(container: HTMLElement): void {
    this.sendButtonContainer = container
  }

  /**
   * 获取最后输入值
   */
  getLastInputValue(): string {
    return this.lastInputValue
  }

  /**
   * 销毁
   */
  destroy(): void {
    this.inputElement = null
    this.sendButtonContainer = null
    this.lastInputValue = ''
  }
}
