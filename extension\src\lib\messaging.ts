import type { ChromeMessage, MessageType } from '@/types'

export class MessagingService {
  // 发送消息到background script
  static async sendToBackground<T = any>(
    type: MessageType,
    payload?: any
  ): Promise<T> {
    const message: ChromeMessage = {
      type,
      payload,
      timestamp: Date.now()
    }

    try {
      const response = await chrome.runtime.sendMessage(message)
      return response
    } catch (error) {
      console.error('【EchoSync】Send to background error:', error)
      throw error
    }
  }

  // 发送消息到content script
  static async sendToContentScript<T = any>(
    tabId: number,
    type: MessageType,
    payload?: any
  ): Promise<T> {
    const message: ChromeMessage = {
      type,
      payload,
      timestamp: Date.now(),
      tabId
    }

    try {
      const response = await chrome.tabs.sendMessage(tabId, message)
      return response
    } catch (error) {
      console.error('【EchoSync】Send to content script error:', error)
      throw error
    }
  }

  // 发送消息到所有匹配的tabs
  static async sendToAllTabs<T = any>(
    type: MessageType,
    payload?: any,
    urlPattern?: string
  ): Promise<T[]> {
    try {
      const tabs = await chrome.tabs.query({
        url: urlPattern || '*://*/*'
      })

      const promises = tabs.map(tab => {
        if (tab.id) {
          return this.sendToContentScript(tab.id, type, payload).catch(() => null)
        }
        return null
      })

      const results = await Promise.all(promises)
      return results.filter(result => result !== null)
    } catch (error) {
      console.error('【EchoSync】Send to all tabs error:', error)
      return []
    }
  }

  // 监听消息
  static onMessage<T = any>(
    callback: (
      message: ChromeMessage<T>,
      sender: chrome.runtime.MessageSender,
      sendResponse: (response?: any) => void
    ) => void | Promise<void>
  ): void {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      const result = callback(message, sender, sendResponse)
      
      // 如果返回Promise，处理异步响应
      if (result instanceof Promise) {
        result
          .then(response => sendResponse(response))
          .catch(error => sendResponse({ error: error.message }))
        return true // 保持消息通道开放
      }
    })
  }

  // 获取当前活动tab
  static async getCurrentTab(): Promise<chrome.tabs.Tab | null> {
    try {
      const [tab] = await chrome.tabs.query({
        active: true,
        currentWindow: true
      })
      return tab || null
    } catch (error) {
      console.error('【EchoSync】Get current tab error:', error)
      return null
    }
  }

  // 检查tab是否支持content script
  static async isTabSupported(tabId: number): Promise<boolean> {
    try {
      const tab = await chrome.tabs.get(tabId)
      if (!tab.url) return false

      const supportedDomains = [
        'chat.openai.com',
        'chat.deepseek.com',
        'claude.ai',
        'gemini.google.com',
        'poe.com',
        'perplexity.ai',
        'you.com'
      ]

      return supportedDomains.some(domain => tab.url!.includes(domain))
    } catch (error) {
      console.error('【EchoSync】Check tab supported error:', error)
      return false
    }
  }
}
